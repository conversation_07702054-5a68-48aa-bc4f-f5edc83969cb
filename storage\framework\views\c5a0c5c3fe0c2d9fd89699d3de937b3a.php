<?php $__env->startSection('title', 'Profil Saya - Dashboard Supplier'); ?>
<?php $__env->startSection('page-title', 'Profil Saya'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Profil Saya</h1>
                    <p class="text-gray-600 mt-1">Kelola informasi akun supplier Anda</p>
                </div>
                <a href="<?php echo e(route('supplier.profile.edit')); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Profil
                </a>
            </div>
        </div>
    </div>

    <!-- Profile Information -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Informasi Akun</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Profile Picture -->
                <div class="md:col-span-2 flex items-center space-x-6">
                    <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
                        <span class="text-blue-600 font-bold text-2xl"><?php echo e(substr($user->name, 0, 1)); ?></span>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900"><?php echo e($user->name); ?></h3>
                        <p class="text-sm text-gray-500">Supplier Administrator</p>
                        <p class="text-sm text-gray-500">Bergabung <?php echo e($user->created_at->format('d F Y')); ?></p>
                    </div>
                </div>

                <!-- Name -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nama Lengkap</label>
                    <div class="px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        <?php echo e($user->name); ?>

                    </div>
                </div>

                <!-- Email -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <div class="px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        <?php echo e($user->email); ?>

                    </div>
                </div>

                <!-- Role -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Peran</label>
                    <div class="px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
                            Supplier Administrator
                        </span>
                    </div>
                </div>

                <!-- Timezone -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Zona Waktu</label>
                    <div class="px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        <?php if($user->timezone === 'Asia/Jakarta'): ?>
                            WIB (Waktu Indonesia Barat)
                        <?php elseif($user->timezone === 'Asia/Makassar'): ?>
                            WITA (Waktu Indonesia Tengah)
                        <?php elseif($user->timezone === 'Asia/Jayapura'): ?>
                            WIT (Waktu Indonesia Timur)
                        <?php else: ?>
                            <?php echo e($user->timezone); ?>

                        <?php endif; ?>
                    </div>
                </div>

                <!-- Account Created -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Akun Dibuat</label>
                    <div class="px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        <?php echo e($user->formatDateTime($user->created_at, 'd F Y H:i')); ?>

                    </div>
                </div>

                <!-- Last Updated -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Terakhir Diperbarui</label>
                    <div class="px-3 py-2 border border-gray-300 rounded-lg bg-gray-50">
                        <?php echo e($user->formatDateTime($user->updated_at, 'd F Y H:i')); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Statistics -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Statistik Akun</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Total Deliveries -->
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">
                        <?php echo e(number_format($user->receivedDeliveries()->count())); ?>

                    </div>
                    <div class="text-sm text-blue-600 font-medium">Pengiriman Diterima</div>
                </div>

                <!-- Requested Returns -->
                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                    <div class="text-2xl font-bold text-yellow-600">
                        <?php echo e(number_format($user->requestedReturns()->count())); ?>

                    </div>
                    <div class="text-sm text-yellow-600 font-medium">Retur Diminta</div>
                </div>

                <!-- Approved Returns -->
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">
                        <?php echo e(number_format($user->approvedReturns()->count())); ?>

                    </div>
                    <div class="text-sm text-green-600 font-medium">Retur Disetujui</div>
                </div>

                <!-- Account Age -->
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600">
                        <?php echo e($user->created_at->diffInDays(now())); ?>

                    </div>
                    <div class="text-sm text-purple-600 font-medium">Hari Bergabung</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Information -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Keamanan Akun</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">Kata Sandi</div>
                            <div class="text-sm text-gray-500">Kata sandi Anda aman dan terenkripsi</div>
                        </div>
                    </div>
                    <a href="<?php echo e(route('supplier.profile.edit')); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Ubah Kata Sandi
                    </a>
                </div>

                <div class="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <div class="font-medium text-gray-900">Email Terverifikasi</div>
                            <div class="text-sm text-gray-500"><?php echo e($user->email); ?></div>
                        </div>
                    </div>
                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        Aktif
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.supplier', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/supplier/profile/show.blade.php ENDPATH**/ ?>