<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Dashboard Supplier - Indah Berkah Abadi')</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700" rel="stylesheet" />

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Supplier Dashboard Specific Styles -->
    <link rel="stylesheet" href="{{ asset('css/supplier-dashboard.css') }}?v={{ time() }}">
    <link rel="stylesheet" href="{{ asset('css/supplier-dashboard-mobile-fixes.css') }}?v={{ time() }}">









    <!-- Additional Styles -->
    @stack('styles')
</head>
<body class="supplier-dashboard-layout">
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebar-overlay" class="supplier-dashboard-sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="supplier-dashboard-sidebar">
        <!-- Logo Section -->
        <div class="supplier-dashboard-sidebar-header">
            <div class="supplier-dashboard-sidebar-brand">
                <div class="supplier-dashboard-logo-icon">IBA</div>
                <div class="supplier-dashboard-logo-text">
                    <div class="supplier-dashboard-logo-title">Indah Berkah Abadi</div>
                    <div class="supplier-dashboard-logo-subtitle">Dashboard Supplier</div>
                </div>
            </div>
        </div>
        
        <!-- Navigation Menu -->
        <nav class="supplier-dashboard-nav">
            <a href="{{ route('supplier.dashboard') }}" class="supplier-dashboard-nav-item {{ request()->routeIs('supplier.dashboard') ? 'supplier-dashboard-nav-item-active' : '' }}">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 5 4-4 4 4"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Dashboard</span>
            </a>

            <a href="{{ route('supplier.deliveries.index') }}" class="supplier-dashboard-nav-item {{ request()->routeIs('supplier.deliveries.*') ? 'supplier-dashboard-nav-item-active' : '' }}">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Pengiriman</span>
            </a>

            <a href="{{ route('supplier.returns.index') }}" class="supplier-dashboard-nav-item {{ request()->routeIs('supplier.returns.*') ? 'supplier-dashboard-nav-item-active' : '' }}">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Retur</span>
            </a>

            <a href="{{ route('supplier.profile.show') }}" class="supplier-dashboard-nav-item {{ request()->routeIs('supplier.profile.*') ? 'supplier-dashboard-nav-item-active' : '' }}">
                <svg class="supplier-dashboard-nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="supplier-dashboard-nav-text">Profil</span>
            </a>
        </nav>
        
        <!-- Sidebar Footer with Profile -->
        <div class="supplier-dashboard-sidebar-footer">
            <div class="supplier-dashboard-user-menu">
                <button class="supplier-dashboard-user-menu-button" id="userMenuButton" type="button" aria-expanded="false" aria-haspopup="true">
                    <div class="supplier-dashboard-user-info">
                        <div class="supplier-dashboard-user-avatar">
                            <span class="supplier-dashboard-user-initial">{{ substr(auth()->user()->name, 0, 1) }}</span>
                        </div>
                        <div class="supplier-dashboard-user-details">
                            <p class="supplier-dashboard-user-name">{{ auth()->user()->name }}</p>
                            <p class="supplier-dashboard-user-role">Supplier Admin</p>
                        </div>
                    </div>
                    <svg class="supplier-dashboard-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div class="supplier-dashboard-user-menu-dropdown" id="userMenuDropdown" role="menu" aria-labelledby="user-menu-button">
                    <a href="{{ route('supplier.profile.show') }}" class="supplier-dashboard-dropdown-item" role="menuitem">
                        <svg class="supplier-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Lihat Profil
                    </a>
                    <div class="supplier-dashboard-dropdown-separator" role="separator"></div>
                    <form method="POST" action="{{ route('logout') }}" class="supplier-dashboard-logout-form">
                        @csrf
                        <button type="submit" class="supplier-dashboard-dropdown-item supplier-dashboard-logout-button" role="menuitem">
                            <svg class="supplier-dashboard-dropdown-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            Keluar
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </aside>
    
    <!-- Main Content -->
    <main class="supplier-dashboard-main">
        <!-- Header -->
        <header class="supplier-dashboard-header">
            <div class="supplier-dashboard-header-left">
                <button class="supplier-dashboard-mobile-menu-btn" onclick="toggleSidebar()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                <div class="supplier-dashboard-breadcrumb">
                    <h1 class="supplier-dashboard-page-title">@yield('page-title', 'Dashboard Supplier')</h1>
                </div>
            </div>

            <div class="supplier-dashboard-header-right">
                <!-- User Dropdown -->
                <div class="supplier-dashboard-user-dropdown">
                    <button class="supplier-dashboard-user-dropdown-btn" onclick="toggleUserMenu()">
                        <div class="supplier-dashboard-user-avatar-small">
                            {{ substr(auth()->user()->name, 0, 1) }}
                        </div>
                        <span class="supplier-dashboard-user-name-small">{{ auth()->user()->name }}</span>
                        <svg class="supplier-dashboard-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div id="user-dropdown-menu" class="supplier-dashboard-user-dropdown-menu">
                        <a href="{{ route('supplier.profile.show') }}" class="supplier-dashboard-dropdown-item">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Profil Saya
                        </a>
                        <div class="supplier-dashboard-dropdown-divider"></div>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="supplier-dashboard-dropdown-item supplier-dashboard-dropdown-item-danger">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Keluar
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Content Area -->
        <div class="supplier-dashboard-content">
            <!-- Flash Messages -->
            @if(session('success'))
                <div class="supplier-dashboard-alert supplier-dashboard-alert-success">
                    <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>{{ session('success') }}</span>
                </div>
            @endif

            @if(session('error'))
                <div class="supplier-dashboard-alert supplier-dashboard-alert-error">
                    <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>{{ session('error') }}</span>
                </div>
            @endif

            @if(session('info'))
                <div class="supplier-dashboard-alert supplier-dashboard-alert-info">
                    <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>{{ session('info') }}</span>
                </div>
            @endif

            <!-- Main Content -->
            @yield('content')
        </div>
    </main>
    
    <!-- JavaScript Files -->
    <!-- Note: app.js is loaded via @vite directive in head section -->

    <!-- Supplier Dashboard JavaScript -->
    <script src="{{ asset('js/supplier-dashboard.js') }}?v={{ time() }}"></script>

    <script>
        // Initialize supplier dashboard functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar functionality
            function toggleSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');

                sidebar.classList.toggle('supplier-dashboard-sidebar-open');
                overlay.classList.toggle('supplier-dashboard-sidebar-overlay-show');
            }

            function closeSidebar() {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');

                sidebar.classList.remove('supplier-dashboard-sidebar-open');
                overlay.classList.remove('supplier-dashboard-sidebar-overlay-show');
            }

            // User menu functionality
            const userMenuButton = document.getElementById('userMenuButton');
            const userMenuDropdown = document.getElementById('userMenuDropdown');

            if (userMenuButton && userMenuDropdown) {
                userMenuButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    userMenuDropdown.classList.toggle('supplier-dashboard-user-menu-dropdown-show');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function() {
                    userMenuDropdown.classList.remove('supplier-dashboard-user-menu-dropdown-show');
                });

                userMenuDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }

            // Header user dropdown functionality
            function toggleUserMenu() {
                const dropdown = document.getElementById('user-dropdown-menu');
                if (dropdown) {
                    dropdown.classList.toggle('supplier-dashboard-dropdown-show');
                }
            }

            // Close header dropdown when clicking outside
            document.addEventListener('click', function(event) {
                const dropdown = document.getElementById('user-dropdown-menu');
                const button = document.querySelector('.supplier-dashboard-user-dropdown-btn');

                if (dropdown && !dropdown.contains(event.target) && !button.contains(event.target)) {
                    dropdown.classList.remove('supplier-dashboard-dropdown-show');
                }
            });

            // Close sidebar when clicking overlay
            const overlay = document.getElementById('sidebar-overlay');
            if (overlay) {
                overlay.addEventListener('click', function() {
                    closeSidebar();
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(event) {
                const sidebar = document.getElementById('sidebar');
                const toggleBtn = document.querySelector('.supplier-dashboard-mobile-menu-btn');

                if (window.innerWidth < 1024 &&
                    sidebar && sidebar.classList.contains('supplier-dashboard-sidebar-open') &&
                    !sidebar.contains(event.target) &&
                    toggleBtn && !toggleBtn.contains(event.target)) {
                    closeSidebar();
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });

            // Make functions globally available
            window.toggleSidebar = toggleSidebar;
            window.closeSidebar = closeSidebar;
            window.toggleUserMenu = toggleUserMenu;
        });
    </script>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>
