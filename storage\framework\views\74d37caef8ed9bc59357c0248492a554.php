<?php $__env->startSection('title', 'Masuk - Indah Berkah Abadi'); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/login.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="iba-login-container">
    <div class="iba-login-card">
        <!-- Header -->
        <div class="iba-login-header">
            <a href="<?php echo e(route('home')); ?>" class="iba-login-back-link">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <PERSON><PERSON><PERSON> ke <PERSON>
            </a>

            <h2 class="iba-login-title">Masuk Sistem</h2>
            <p class="iba-login-subtitle">Akses dashboard manajemen inventori Anda</p>
        </div>

        <!-- Form -->
        <div class="iba-login-form">
            <?php if(session('success')): ?>
                <div class="iba-login-alert iba-login-alert-success">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('info')): ?>
                <div class="iba-login-alert iba-login-alert-info">
                    <?php echo e(session('info')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="iba-login-alert iba-login-alert-error">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo e(route('login.post')); ?>">
                <?php echo csrf_field(); ?>
                
                <div class="iba-login-form-group">
                    <label for="email" class="iba-login-label">Email</label>
                    <input 
                        id="email" 
                        name="email" 
                        type="email" 
                        class="iba-login-input <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> iba-login-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Masukkan email Anda"
                        value="<?php echo e(old('email')); ?>"
                        required
                        autocomplete="email"
                        autofocus
                    >
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="iba-login-error"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="iba-login-form-group">
                    <label for="password" class="iba-login-label">Kata Sandi</label>
                    <input 
                        id="password" 
                        name="password" 
                        type="password" 
                        class="iba-login-input <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> iba-login-input-error <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                        placeholder="Masukkan kata sandi"
                        required
                        autocomplete="current-password"
                    >
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="iba-login-error"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <button type="submit" class="iba-login-btn">
                    Masuk ke Dashboard
                </button>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/login.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/auth/login.blade.php ENDPATH**/ ?>