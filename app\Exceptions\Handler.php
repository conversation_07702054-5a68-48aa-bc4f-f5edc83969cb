<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Auth\AuthenticationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });

        // Register custom rendering for specific exceptions
        $this->renderable(function (AuthenticationException $e, $request) {
            return $this->unauthenticated($request, $e);
        });

        $this->renderable(function (HttpException $e, $request) {
            return $this->renderHttpException($e, $request);
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e)
    {
        // Handle authentication exceptions
        if ($e instanceof AuthenticationException) {
            return $this->unauthenticated($request, $e);
        }

        // Handle HTTP exceptions with custom error pages
        if ($e instanceof HttpException) {
            return $this->renderHttpException($e, $request);
        }

        // Handle 500 errors with role-based redirects
        if ($this->shouldRenderCustomErrorPage($e)) {
            return $this->renderCustomErrorPage($request, $e);
        }

        return parent::render($request, $e);
    }

    /**
     * Convert an authentication exception into a response.
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->expectsJson()) {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        }

        return redirect()->guest(route('login'));
    }

    /**
     * Render HTTP exceptions with custom error pages
     */
    protected function renderHttpException(HttpException $e, Request $request)
    {
        $status = $e->getStatusCode();

        // Handle specific HTTP status codes
        switch ($status) {
            case 403:
                return response()->view('errors.403', [
                    'exception' => $e
                ], 403);

            case 404:
                return response()->view('errors.404', [
                    'exception' => $e
                ], 404);

            case 500:
                return response()->view('errors.500', [
                    'exception' => $e
                ], 500);

            default:
                // For other HTTP errors, use Laravel's default handling
                return parent::render($request, $e);
        }
    }

    /**
     * Determine if we should render a custom error page
     */
    protected function shouldRenderCustomErrorPage(Throwable $e): bool
    {
        // Render custom error page for server errors (500-level)
        return !config('app.debug') && 
               !request()->expectsJson() && 
               $this->isHttpException($e) === false;
    }

    /**
     * Render custom error page with role-based fallback
     */
    protected function renderCustomErrorPage(Request $request, Throwable $e)
    {
        // Log the error for debugging
        $this->report($e);

        // Return 500 error page with role-based navigation
        return response()->view('errors.500', [
            'exception' => $e
        ], 500);
    }

    /**
     * Handle fallback routing for undefined routes
     */
    public function handleFallback(Request $request)
    {
        if (auth()->check()) {
            $user = auth()->user();

            if ($user->isAdmin()) {
                return redirect()->route('admin.dashboard');
            } elseif ($user->isSupplierAdmin()) {
                return redirect()->route('supplier.dashboard');
            }

            return redirect()->route('user.dashboard');
        }

        return redirect()->route('login');
    }
}
