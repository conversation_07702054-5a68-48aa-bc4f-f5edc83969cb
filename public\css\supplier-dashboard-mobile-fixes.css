/* Indah Berkah Abadi - Supplier Dashboard Mobile Z-Index Fixes */
/* Independent CSS for mobile accessibility and z-index management */
/*
 * COMPREHENSIVE Z-INDEX HIERARCHY FOR MOBILE ACCESSIBILITY:
 * - Sidebar navigation: 1000+ (highest priority)
 * - Modal overlays: 500-999 (high priority, below sidebar)
 * - Dropdown elements: 100-499 (medium-high priority)
 * - Interactive form elements: 50-99 (medium priority)
 * - Buttons and clickable elements: 10-49 (low-medium priority)
 * - Hover states and tooltips: 2-9 (low priority)
 * - Main content: 1 (lowest priority)
 */

/* ===== MOBILE-SPECIFIC Z-INDEX FIXES ===== */

/* Apply fixes only on mobile devices */
@media (max-width: 768px) {
    /* Sidebar - highest priority */
    .supplier-dashboard-sidebar {
        z-index: 1000 !important;
        position: fixed !important;
    }

    /* Sidebar overlay */
    .supplier-dashboard-sidebar-overlay {
        z-index: 999 !important;
        position: fixed !important;
    }

    /* Mobile hamburger menu button - ensure it's clickable */
    .supplier-dashboard-mobile-menu-btn {
        z-index: 50 !important;
        position: relative !important;
        pointer-events: auto !important;
        cursor: pointer !important;
        touch-action: manipulation !important;
        -webkit-tap-highlight-color: transparent !important;
    }

    /* Ensure the button is always on top and clickable */
    .supplier-dashboard-header-left {
        z-index: 51 !important;
        position: relative !important;
    }

    /* Header dropdowns */
    .supplier-dashboard-user-dropdown-menu {
        z-index: 500 !important;
        position: absolute !important;
    }

    /* Sidebar user menu dropdown */
    .supplier-dashboard-user-menu-dropdown {
        z-index: 1100 !important;
        position: absolute !important;
    }

    /* Enhanced form elements - highest interactive priority */
    .supplier-dashboard-form-select-enhanced,
    .supplier-dashboard-form-group-enhanced .supplier-dashboard-form-select {
        z-index: 60 !important;
        position: relative !important;
    }

    /* Enhanced form elements when focused */
    .supplier-dashboard-form-select-enhanced:focus,
    .supplier-dashboard-form-group-enhanced .supplier-dashboard-form-select:focus {
        z-index: 70 !important;
        position: relative !important;
    }

    /* Enhanced containers */
    .supplier-dashboard-dropdown-container-enhanced,
    .supplier-dashboard-filter-group-enhanced,
    .supplier-dashboard-form-group-enhanced {
        z-index: 60 !important;
        position: relative !important;
    }

    /* Header action buttons */
    .supplier-dashboard-header-actions .supplier-dashboard-btn,
    .supplier-dashboard-header-actions .supplier-dashboard-btn-sm,
    .supplier-dashboard-header-actions a {
        z-index: 30 !important;
        position: relative !important;
    }

    /* Card action buttons */
    .supplier-dashboard-card-actions .supplier-dashboard-btn,
    .supplier-dashboard-card-actions .supplier-dashboard-btn-sm,
    .supplier-dashboard-card-actions a {
        z-index: 25 !important;
        position: relative !important;
    }

    /* Interactive elements */
    .supplier-dashboard-btn,
    .supplier-dashboard-btn-sm,
    .supplier-dashboard-input,
    .supplier-dashboard-select,
    .supplier-dashboard-textarea {
        z-index: 20 !important;
        position: relative !important;
    }

    /* Form elements */
    .supplier-dashboard-form-input,
    .supplier-dashboard-form-select,
    .supplier-dashboard-form-textarea {
        z-index: 15 !important;
        position: relative !important;
    }

    /* Pagination buttons */
    .supplier-dashboard-pagination-btn {
        z-index: 15 !important;
        position: relative !important;
    }

    /* Badge elements */
    .supplier-dashboard-badge {
        z-index: 10 !important;
        position: relative !important;
    }

    /* Header elements */
    .supplier-dashboard-header {
        z-index: 100 !important;
        position: sticky !important;
    }

    /* Main content */
    .supplier-dashboard-main {
        z-index: 1 !important;
        position: relative !important;
    }

    /* Content area */
    .supplier-dashboard-content {
        z-index: 1 !important;
        position: relative !important;
    }
}

/* ===== MOBILE LAYOUT FIXES ===== */

@media (max-width: 768px) {
    /* Ensure proper body scroll behavior */
    body.supplier-sidebar-open {
        overflow: hidden;
    }

    /* Fix sidebar width on small screens */
    .supplier-dashboard-sidebar {
        width: calc(100vw - 2rem);
        max-width: 280px;
    }

    /* Ensure touch targets meet accessibility requirements */
    .supplier-dashboard-nav-item,
    .supplier-dashboard-user-menu-button,
    .supplier-dashboard-dropdown-item,
    .supplier-dashboard-mobile-menu-btn {
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* Improve touch interaction for mobile menu button */
    .supplier-dashboard-mobile-menu-btn {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    /* Prevent text selection on interactive elements */
    .supplier-dashboard-mobile-menu-btn,
    .supplier-dashboard-nav-item,
    .supplier-dashboard-user-menu-button {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

@media (max-width: 768px) {
    /* Focus states for better accessibility */
    .supplier-dashboard-mobile-menu-btn:focus,
    .supplier-dashboard-nav-item:focus,
    .supplier-dashboard-btn:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .supplier-dashboard-sidebar {
            border-right: 2px solid #000;
        }

        .supplier-dashboard-mobile-menu-btn {
            border: 2px solid #000;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .supplier-dashboard-sidebar,
        .supplier-dashboard-sidebar-overlay,
        .supplier-dashboard-mobile-menu-btn {
            transition: none;
        }
    }
}

/* ===== EXTRA SMALL MOBILE FIXES ===== */

@media (max-width: 375px) {
    .supplier-dashboard-sidebar {
        width: calc(100vw - 1rem);
        max-width: none;
    }

    .supplier-dashboard-content {
        padding: 0.5rem;
    }

    .supplier-dashboard-header {
        padding: 0.75rem;
    }
}

/* ===== LANDSCAPE MOBILE FIXES ===== */

@media (max-width: 768px) and (orientation: landscape) {
    .supplier-dashboard-sidebar {
        width: 280px;
        max-width: 50vw;
    }
}

/* ===== DEBUGGING HELPERS (Remove in production) ===== */

/* Uncomment for debugging z-index issues */
/*
@media (max-width: 768px) {
    .supplier-dashboard-sidebar {
        border: 2px solid red !important;
    }

    .supplier-dashboard-mobile-menu-btn {
        border: 2px solid blue !important;
        background: rgba(0, 0, 255, 0.3) !important;
    }

    .supplier-dashboard-sidebar-overlay {
        border: 2px solid green !important;
    }

    .supplier-dashboard-header-left {
        border: 2px solid orange !important;
    }
}
*/

/* ===== FINAL MOBILE ACCESSIBILITY FIXES ===== */

@media (max-width: 768px) {
    /* Ensure all interactive elements are properly layered */
    .supplier-dashboard-mobile-menu-btn,
    .supplier-dashboard-mobile-menu-btn * {
        pointer-events: auto !important;
        z-index: 50 !important;
    }

    /* Prevent any overlay from blocking the menu button */
    .supplier-dashboard-header {
        position: relative !important;
        z-index: 100 !important;
    }

    .supplier-dashboard-header-left {
        position: relative !important;
        z-index: 101 !important;
    }

    /* Ensure sidebar doesn't interfere with button clicks when closed */
    .supplier-dashboard-sidebar:not(.supplier-dashboard-sidebar-open) {
        pointer-events: none !important;
    }

    .supplier-dashboard-sidebar.supplier-dashboard-sidebar-open {
        pointer-events: auto !important;
    }
}
