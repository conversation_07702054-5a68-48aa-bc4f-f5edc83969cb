/* Indah Berkah Abadi - Supplier Dashboard JavaScript */
/* Independent JavaScript for supplier dashboard functionality */

// Supplier Dashboard Utilities
const SupplierDashboard = {
    // Initialize all dashboard functionality
    init: function() {
        this.initSidebar();
        this.initUserMenu();
        this.initDropdowns();
        this.initMobileHandlers();
        this.initFormValidation();
    },

    // Sidebar functionality
    initSidebar: function() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const toggleBtn = document.querySelector('.supplier-dashboard-mobile-menu-btn');

        if (toggleBtn) {
            toggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                SupplierDashboard.toggleSidebar();
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                SupplierDashboard.closeSidebar();
            });
        }

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                SupplierDashboard.closeSidebar();
            }
        });
    },

    // User menu functionality
    initUserMenu: function() {
        const userMenuButton = document.getElementById('userMenuButton');
        const userMenuDropdown = document.getElementById('userMenuDropdown');

        if (userMenuButton && userMenuDropdown) {
            userMenuButton.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenuDropdown.classList.toggle('supplier-dashboard-user-menu-dropdown-show');
                
                // Update aria-expanded
                const isExpanded = userMenuDropdown.classList.contains('supplier-dashboard-user-menu-dropdown-show');
                userMenuButton.setAttribute('aria-expanded', isExpanded);
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                userMenuDropdown.classList.remove('supplier-dashboard-user-menu-dropdown-show');
                userMenuButton.setAttribute('aria-expanded', 'false');
            });

            userMenuDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }
    },

    // General dropdown functionality
    initDropdowns: function() {
        const dropdowns = document.querySelectorAll('.supplier-dashboard-dropdown');
        
        dropdowns.forEach(function(dropdown) {
            const trigger = dropdown.querySelector('.supplier-dashboard-dropdown-trigger');
            const menu = dropdown.querySelector('.supplier-dashboard-dropdown-menu');
            
            if (trigger && menu) {
                trigger.addEventListener('click', function(e) {
                    e.stopPropagation();
                    
                    // Close other dropdowns
                    dropdowns.forEach(function(otherDropdown) {
                        if (otherDropdown !== dropdown) {
                            otherDropdown.querySelector('.supplier-dashboard-dropdown-menu')?.classList.remove('show');
                        }
                    });
                    
                    menu.classList.toggle('show');
                });
            }
        });

        // Close all dropdowns when clicking outside
        document.addEventListener('click', function() {
            dropdowns.forEach(function(dropdown) {
                dropdown.querySelector('.supplier-dashboard-dropdown-menu')?.classList.remove('show');
            });
        });
    },

    // Mobile-specific handlers
    initMobileHandlers: function() {
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) {
                SupplierDashboard.closeSidebar();
            }
        });

        // Prevent body scroll when sidebar is open on mobile
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const isOpen = sidebar.classList.contains('supplier-dashboard-sidebar-open');
                        if (window.innerWidth < 1024) {
                            document.body.style.overflow = isOpen ? 'hidden' : '';
                        }
                    }
                });
            });
            
            observer.observe(sidebar, { attributes: true });
        }
    },

    // Form validation helpers
    initFormValidation: function() {
        const forms = document.querySelectorAll('.supplier-dashboard-form');
        
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('supplier-dashboard-input-error');
                    } else {
                        field.classList.remove('supplier-dashboard-input-error');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    SupplierDashboard.showAlert('Mohon lengkapi semua field yang wajib diisi.', 'error');
                }
            });
        });
    },

    // Sidebar toggle functions
    toggleSidebar: function() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (sidebar && overlay) {
            const isOpen = sidebar.classList.contains('supplier-dashboard-sidebar-open');

            if (isOpen) {
                sidebar.classList.remove('supplier-dashboard-sidebar-open');
                overlay.classList.remove('supplier-dashboard-sidebar-overlay-show');
                document.body.classList.remove('supplier-sidebar-open');

                // Restore scrolling on mobile
                if (window.innerWidth <= 768) {
                    document.body.style.overflow = '';
                    document.body.style.position = '';
                    document.body.style.width = '';
                }
            } else {
                sidebar.classList.add('supplier-dashboard-sidebar-open');
                overlay.classList.add('supplier-dashboard-sidebar-overlay-show');
                document.body.classList.add('supplier-sidebar-open');

                // Prevent background scrolling on mobile
                if (window.innerWidth <= 768) {
                    document.body.style.overflow = 'hidden';
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                }
            }
        }
    },

    closeSidebar: function() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (sidebar && overlay) {
            sidebar.classList.remove('supplier-dashboard-sidebar-open');
            overlay.classList.remove('supplier-dashboard-sidebar-overlay-show');
            document.body.classList.remove('supplier-sidebar-open');

            // Restore scrolling
            document.body.style.overflow = '';
            document.body.style.position = '';
            document.body.style.width = '';
        }
    },

    // Alert system
    showAlert: function(message, type = 'info') {
        const alertContainer = document.querySelector('.supplier-dashboard-content');
        if (!alertContainer) return;

        const alertElement = document.createElement('div');
        alertElement.className = `supplier-dashboard-alert supplier-dashboard-alert-${type}`;
        alertElement.innerHTML = `
            <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>${message}</span>
        `;

        alertContainer.insertBefore(alertElement, alertContainer.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(function() {
            if (alertElement.parentNode) {
                alertElement.remove();
            }
        }, 5000);
    },

    // Loading state helpers
    showLoading: function(element) {
        if (element) {
            element.classList.add('supplier-dashboard-loading');
            element.disabled = true;
        }
    },

    hideLoading: function(element) {
        if (element) {
            element.classList.remove('supplier-dashboard-loading');
            element.disabled = false;
        }
    },

    // Utility functions
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(amount);
    },

    formatDate: function(date) {
        return new Intl.DateTimeFormat('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    SupplierDashboard.init();
});

// Make functions globally available for inline handlers
window.toggleSidebar = function() {
    SupplierDashboard.toggleSidebar();
};

window.closeSidebar = function() {
    SupplierDashboard.closeSidebar();
};

window.toggleUserMenu = function() {
    const dropdown = document.getElementById('user-dropdown-menu');
    if (dropdown) {
        dropdown.classList.toggle('supplier-dashboard-dropdown-show');
    }
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupplierDashboard;
}
